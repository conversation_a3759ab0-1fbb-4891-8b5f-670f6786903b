#!/usr/bin/env python3
"""
Apply Week 3 Database Migration
Applies schema updates for multi-practice area integration and cross-document relationships.

This script:
1. Applies Week 3 schema migration to Supabase
2. Creates new tables for document relationships and practice area analysis
3. Updates existing tables with new fields
4. Validates the migration results
5. Provides rollback capabilities if needed
"""

import os
import sys
import logging
from typing import Dict, List, Optional
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.processing.storage.supabase_connector import SupabaseConnector

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Week3MigrationManager:
    """Manages the Week 3 database migration process."""
    
    def __init__(self):
        """Initialize the migration manager."""
        self.supabase = SupabaseConnector()
        self.migration_file = os.path.join(os.path.dirname(__file__), '..', 'migrations', 'week3_schema_updates.sql')
        
    def load_migration_sql(self) -> str:
        """Load the migration SQL from file."""
        try:
            with open(self.migration_file, 'r') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Failed to load migration file: {e}")
            raise
    
    def check_prerequisites(self) -> bool:
        """Check if prerequisites for migration are met."""
        logger.info("Checking Week 3 migration prerequisites...")
        
        try:
            # Test database connection
            result = self.supabase.execute_sql("SELECT 1 as test")
            if not result:
                logger.error("Database connection test failed")
                return False
            
            # Check if Week 1 and Week 2 migrations have been applied
            existing_tables = self.supabase.execute_sql("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('jurisdictions', 'document_types', 'cases', 'documents')
            """)
            
            if not existing_tables or len(existing_tables) < 4:
                logger.error("Week 1 and Week 2 migrations must be applied first")
                return False
            
            # Check if Week 3 migration has already been applied
            week3_tables = self.supabase.execute_sql("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('document_relationships', 'practice_area_relationships', 'missing_statutes')
            """)
            
            if week3_tables and len(week3_tables) > 0:
                logger.warning("Week 3 migration tables already exist. This migration may have been applied before.")
                response = input("Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    return False
            
            logger.info("Prerequisites check passed")
            return True
            
        except Exception as e:
            logger.error(f"Prerequisites check failed: {e}")
            return False
    
    def backup_existing_data(self) -> bool:
        """Create backup of existing data before migration."""
        logger.info("Creating backup of existing data...")
        
        try:
            backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Backup existing tables that will be modified
            tables_to_backup = ['documents', 'cases', 'case_citations']
            
            for table in tables_to_backup:
                backup_table = f"{table}_backup_week3_{backup_timestamp}"
                
                # Create backup table
                backup_sql = f"""
                CREATE TABLE {backup_table} AS 
                SELECT * FROM {table};
                """
                
                result = self.supabase.execute_sql(backup_sql)
                if result is None:
                    logger.error(f"Failed to backup table {table}")
                    return False
                
                logger.info(f"Backed up {table} to {backup_table}")
            
            logger.info("Backup completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
    
    def apply_migration(self) -> bool:
        """Apply the Week 3 migration."""
        logger.info("Applying Week 3 migration...")
        
        try:
            # Create the migration SQL content directly since file might not exist yet
            migration_sql = """
            -- Week 3 Schema Updates: Multi-Practice Area Integration & Cross-Document Relationships
            
            -- Enhanced relationships table
            CREATE TABLE IF NOT EXISTS document_relationships (
                id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
                source_document_id TEXT NOT NULL,
                target_document_id TEXT NOT NULL,
                relationship_type TEXT NOT NULL, -- 'cites', 'distinguishes', 'overrules', 'follows'
                source_practice_area TEXT,
                target_practice_area TEXT,
                cross_practice_area BOOLEAN DEFAULT FALSE,
                citation_text TEXT,
                context_text TEXT,
                confidence_score FLOAT DEFAULT 0.0,
                relationship_strength TEXT, -- 'strong', 'medium', 'weak'
                jurisdiction TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                validated BOOLEAN DEFAULT FALSE,
                validation_source TEXT,
                metadata JSONB
            );
            
            -- Practice area relationships
            CREATE TABLE IF NOT EXISTS practice_area_relationships (
                id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
                source_practice_area TEXT NOT NULL,
                target_practice_area TEXT NOT NULL,
                relationship_count INTEGER DEFAULT 0,
                relationship_strength FLOAT DEFAULT 0.0,
                common_topics TEXT[],
                jurisdiction TEXT,
                last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                metadata JSONB
            );
            
            -- Missing statutes tracking
            CREATE TABLE IF NOT EXISTS missing_statutes (
                id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
                citation_text TEXT NOT NULL,
                normalized_citation TEXT,
                jurisdiction TEXT,
                practice_area TEXT,
                frequency INTEGER DEFAULT 1,
                first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                priority_score FLOAT DEFAULT 0.0,
                collection_status TEXT DEFAULT 'pending', -- 'pending', 'in_progress', 'collected', 'not_available'
                metadata JSONB
            );
            
            -- Citation network metrics
            CREATE TABLE IF NOT EXISTS citation_network_metrics (
                id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
                document_id TEXT NOT NULL,
                practice_area TEXT,
                jurisdiction TEXT,
                authority_score FLOAT DEFAULT 0.0,
                centrality_score FLOAT DEFAULT 0.0,
                influence_score FLOAT DEFAULT 0.0,
                citation_count_incoming INTEGER DEFAULT 0,
                citation_count_outgoing INTEGER DEFAULT 0,
                cross_practice_citations INTEGER DEFAULT 0,
                calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                metadata JSONB
            );
            
            -- Add practice area fields to existing tables
            ALTER TABLE documents ADD COLUMN IF NOT EXISTS practice_areas TEXT[] DEFAULT '{}';
            ALTER TABLE documents ADD COLUMN IF NOT EXISTS primary_practice_area TEXT;
            ALTER TABLE documents ADD COLUMN IF NOT EXISTS cross_practice_references INTEGER DEFAULT 0;
            
            ALTER TABLE cases ADD COLUMN IF NOT EXISTS practice_areas TEXT[] DEFAULT '{}';
            ALTER TABLE cases ADD COLUMN IF NOT EXISTS primary_practice_area TEXT;
            ALTER TABLE cases ADD COLUMN IF NOT EXISTS cross_practice_references INTEGER DEFAULT 0;
            
            -- Add relationship tracking to case_citations
            ALTER TABLE case_citations ADD COLUMN IF NOT EXISTS relationship_type TEXT DEFAULT 'cites';
            ALTER TABLE case_citations ADD COLUMN IF NOT EXISTS confidence_score FLOAT DEFAULT 0.0;
            ALTER TABLE case_citations ADD COLUMN IF NOT EXISTS practice_area_source TEXT;
            ALTER TABLE case_citations ADD COLUMN IF NOT EXISTS practice_area_target TEXT;
            
            -- Create indexes for performance
            CREATE INDEX IF NOT EXISTS idx_document_relationships_source ON document_relationships(source_document_id);
            CREATE INDEX IF NOT EXISTS idx_document_relationships_target ON document_relationships(target_document_id);
            CREATE INDEX IF NOT EXISTS idx_document_relationships_practice_area ON document_relationships(source_practice_area, target_practice_area);
            CREATE INDEX IF NOT EXISTS idx_document_relationships_cross_practice ON document_relationships(cross_practice_area) WHERE cross_practice_area = true;
            
            CREATE INDEX IF NOT EXISTS idx_practice_area_relationships_source ON practice_area_relationships(source_practice_area);
            CREATE INDEX IF NOT EXISTS idx_practice_area_relationships_target ON practice_area_relationships(target_practice_area);
            CREATE INDEX IF NOT EXISTS idx_practice_area_relationships_jurisdiction ON practice_area_relationships(jurisdiction);
            
            CREATE INDEX IF NOT EXISTS idx_missing_statutes_citation ON missing_statutes(normalized_citation);
            CREATE INDEX IF NOT EXISTS idx_missing_statutes_practice_area ON missing_statutes(practice_area);
            CREATE INDEX IF NOT EXISTS idx_missing_statutes_priority ON missing_statutes(priority_score DESC);
            
            CREATE INDEX IF NOT EXISTS idx_citation_network_metrics_document ON citation_network_metrics(document_id);
            CREATE INDEX IF NOT EXISTS idx_citation_network_metrics_practice_area ON citation_network_metrics(practice_area);
            CREATE INDEX IF NOT EXISTS idx_citation_network_metrics_authority ON citation_network_metrics(authority_score DESC);
            
            CREATE INDEX IF NOT EXISTS idx_documents_practice_areas ON documents USING GIN(practice_areas);
            CREATE INDEX IF NOT EXISTS idx_documents_primary_practice_area ON documents(primary_practice_area);
            
            CREATE INDEX IF NOT EXISTS idx_cases_practice_areas ON cases USING GIN(practice_areas);
            CREATE INDEX IF NOT EXISTS idx_cases_primary_practice_area ON cases(primary_practice_area);
            """
            
            # Execute the migration
            result = self.supabase.execute_sql(migration_sql)
            if result is None:
                logger.error("Migration execution failed")
                return False
            
            logger.info("Week 3 migration applied successfully")
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def validate_migration(self) -> bool:
        """Validate that the migration was applied correctly."""
        logger.info("Validating Week 3 migration...")
        
        try:
            # Check that new tables exist
            required_tables = [
                'document_relationships',
                'practice_area_relationships', 
                'missing_statutes',
                'citation_network_metrics'
            ]
            
            for table in required_tables:
                result = self.supabase.execute_sql(f"""
                    SELECT COUNT(*) as count 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = '{table}'
                """)
                
                if not result or result[0]['count'] == 0:
                    logger.error(f"Required table {table} was not created")
                    return False
                
                logger.info(f"✅ Table {table} exists")
            
            # Check that new columns were added
            column_checks = [
                ('documents', 'practice_areas'),
                ('documents', 'primary_practice_area'),
                ('cases', 'practice_areas'),
                ('cases', 'primary_practice_area'),
                ('case_citations', 'relationship_type')
            ]
            
            for table, column in column_checks:
                result = self.supabase.execute_sql(f"""
                    SELECT COUNT(*) as count
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = '{table}' 
                    AND column_name = '{column}'
                """)
                
                if not result or result[0]['count'] == 0:
                    logger.error(f"Required column {table}.{column} was not created")
                    return False
                
                logger.info(f"✅ Column {table}.{column} exists")
            
            logger.info("Migration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Migration validation failed: {e}")
            return False

def main():
    """Main migration function"""
    print("🚀 Week 3 Database Migration")
    print("=" * 50)
    
    migration_manager = Week3MigrationManager()
    
    try:
        # Step 1: Check prerequisites
        if not migration_manager.check_prerequisites():
            logger.error("Prerequisites check failed. Aborting migration.")
            return 1
        
        # Step 2: Create backup
        if not migration_manager.backup_existing_data():
            logger.error("Backup failed. Aborting migration.")
            return 1
        
        # Step 3: Apply migration
        if not migration_manager.apply_migration():
            logger.error("Migration failed. Check logs for details.")
            return 1
        
        # Step 4: Validate migration
        if not migration_manager.validate_migration():
            logger.error("Migration validation failed. Migration may be incomplete.")
            return 1
        
        print("\n" + "=" * 50)
        print("✅ Week 3 migration completed successfully!")
        print("=" * 50)
        print("\nNext steps:")
        print("1. Run: python scripts/configure_week3_settings.py")
        print("2. Start implementing Week 3 relationship detection")
        print("3. Test the new functionality")
        
        return 0
        
    except Exception as e:
        logger.error(f"Migration failed with unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
